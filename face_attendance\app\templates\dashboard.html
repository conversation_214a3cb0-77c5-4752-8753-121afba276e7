<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Face Attendance System - Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --light-bg: #f8f9fa;
            --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --border-radius: 12px;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            margin: 20px;
            padding: 30px;
            backdrop-filter: blur(10px);
        }

        .card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
        }

        .card-stats {
            background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
            color: white;
            border-radius: var(--border-radius);
        }

        .card-stats.success {
            background: linear-gradient(135deg, var(--success-color), #229954);
        }

        .card-stats.warning {
            background: linear-gradient(135deg, var(--warning-color), #d68910);
        }

        .card-stats.danger {
            background: linear-gradient(135deg, var(--danger-color), #c0392b);
        }

        .camera-status {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
            box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
        }
        .camera-active { background-color: var(--success-color); }
        .camera-inactive { background-color: var(--danger-color); }

        /* Compact Camera Feed Styles */
        .camera-feeds-compact {
            position: fixed;
            top: 80px;
            right: 20px;
            width: 320px;
            max-height: 400px;
            z-index: 1000;
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            overflow: hidden;
        }

        .camera-feed-compact {
            width: 100%;
            height: 120px;
            object-fit: cover;
            border-bottom: 1px solid #eee;
        }

        .camera-feed-container-compact {
            position: relative;
            margin-bottom: 0;
        }

        .camera-label-compact {
            position: absolute;
            bottom: 5px;
            left: 5px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: 500;
        }

        .no-camera-placeholder-compact {
            width: 100%;
            height: 120px;
            background: var(--light-bg);
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            color: #6c757d;
            font-size: 12px;
        }

        /* Currently Logged In Emphasis */
        .current-users-card {
            background: linear-gradient(135deg, #ffffff, #f8f9fa);
            border-left: 5px solid var(--success-color);
        }

        .user-row {
            padding: 12px;
            margin: 8px 0;
            background: white;
            border-radius: 8px;
            border-left: 4px solid var(--secondary-color);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            transition: all 0.2s ease;
        }

        .user-row:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .user-name {
            font-weight: 600;
            color: var(--primary-color);
            font-size: 1.1em;
        }

        .user-details {
            color: #6c757d;
            font-size: 0.9em;
            margin-top: 4px;
        }

        /* Active navbar item styling */
        .navbar-nav .nav-link.active {
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: #fff !important;
            font-weight: 600;
        }

        .navbar-nav .nav-link.active:hover {
            background-color: rgba(255, 255, 255, 0.3);
        }

        .navbar {
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .section-title {
            color: var(--primary-color);
            font-weight: 700;
            margin-bottom: 20px;
            border-bottom: 3px solid var(--secondary-color);
            padding-bottom: 10px;
        }

        .stats-icon {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .table {
            border-radius: 8px;
            overflow: hidden;
        }

        .table thead th {
            background: var(--primary-color);
            color: white;
            border: none;
            font-weight: 600;
        }

        .badge {
            font-size: 0.75em;
            padding: 6px 10px;
            border-radius: 20px;
        }

        .btn {
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .btn:hover {
            transform: translateY(-1px);
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .camera-feeds-compact {
                position: relative;
                top: auto;
                right: auto;
                width: 100%;
                margin-bottom: 20px;
            }

            .main-container {
                margin: 10px;
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-eye"></i> VigilantEye
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link active fw-bold" href="/">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </a>
                <a class="nav-link" href="/register-user">
                    <i class="fas fa-user-plus"></i> Register User
                </a>
                <a class="nav-link" href="/manage-camera">
                    <i class="fas fa-video"></i> Manage Cameras
                </a>
                <a class="nav-link" href="/attendance">
                    <i class="fas fa-clock"></i> Attendance Logs
                </a>
                <a class="nav-link" href="/users">
                    <i class="fas fa-users"></i> Users
                </a>
                <div class="navbar-nav ms-3">
                    <form method="post" action="/logout" style="margin: 0;">
                        <button type="submit" class="nav-link btn btn-link text-light border-0" style="background: none;">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </nav>

    <!-- Compact Camera Feeds - Top Right -->
    {% if active_cameras %}
    <div class="camera-feeds-compact d-none d-md-block">
        <div class="card-header bg-primary text-white py-2">
            <h6 class="mb-0"><i class="fas fa-video"></i> Live Cameras</h6>
        </div>
        {% for camera in active_cameras %}
        <div class="camera-feed-container-compact">
            <img src="/camera-stream/{{ camera.camera_id }}"
                 alt="{{ camera.camera_name }}"
                 class="camera-feed-compact"
                 data-camera-id="{{ camera.camera_id }}"
                 onload="this.style.display='block'; this.nextElementSibling.style.display='none';"
                 onerror="handleCameraError(this);">
            <div class="no-camera-placeholder-compact" style="display: none;">
                <i class="fas fa-video-slash mb-1"></i>
                <small>Offline</small>
            </div>
            <div class="camera-label-compact">
                {{ camera.camera_name }}
            </div>
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <div class="main-container">
        <div class="row">
            <div class="col-md-12">
                <h1 class="section-title">
                    <i class="fas fa-tachometer-alt me-3"></i> Dashboard Overview
                </h1>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="row mb-5">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card card-stats success">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-2 opacity-75">Currently Logged In</h6>
                                <h2 class="mb-0 fw-bold">{{ current_users|length }}</h2>
                                <small class="opacity-75">Active Users</small>
                            </div>
                            <div class="stats-icon">
                                <i class="fas fa-user-check fa-lg"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card card-stats">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-2 opacity-75">Active Cameras</h6>
                                <h2 class="mb-0 fw-bold">{{ active_cameras|length }}</h2>
                                <small class="opacity-75">Live Streams</small>
                            </div>
                            <div class="stats-icon">
                                <i class="fas fa-video fa-lg"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card card-stats warning">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-2 opacity-75">Today's Attendance</h6>
                                <h2 class="mb-0 fw-bold" id="today-attendance-count">{{ today_total }}</h2>
                                <small class="opacity-75">Total Records</small>
                            </div>
                            <div class="stats-icon">
                                <i class="fas fa-calendar-check fa-lg"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card card-stats">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-2 opacity-75">System Status</h6>
                                <h5 class="mb-0 fw-bold">
                                    <i class="fas fa-circle text-success me-2"></i>Online
                                </h5>
                                <small class="opacity-75">All Systems</small>
                            </div>
                            <div class="stats-icon">
                                <i class="fas fa-server fa-lg"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mobile Camera Feeds (shown only on mobile) -->
        {% if active_cameras %}
        <div class="row mb-4 d-md-none">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0"><i class="fas fa-video"></i> Live Camera Feeds</h6>
                    </div>
                    <div class="card-body p-2">
                        <div class="row">
                            {% for camera in active_cameras %}
                            <div class="col-6 mb-2">
                                <div class="camera-feed-container-compact">
                                    <img src="/camera-stream/{{ camera.camera_id }}"
                                         alt="{{ camera.camera_name }}"
                                         class="camera-feed-compact"
                                         data-camera-id="{{ camera.camera_id }}"
                                         onload="this.style.display='block'; this.nextElementSibling.style.display='none';"
                                         onerror="handleCameraError(this);">
                                    <div class="no-camera-placeholder-compact" style="display: none;">
                                        <i class="fas fa-video-slash mb-1"></i>
                                        <small>Offline</small>
                                    </div>
                                    <div class="camera-label-compact">
                                        {{ camera.camera_name }}
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Currently Logged In Users - Prominent Section -->
        <div class="row mb-5">
            <div class="col-lg-8 col-md-12">
                <div class="card current-users-card">
                    <div class="card-header bg-gradient" style="background: linear-gradient(135deg, var(--success-color), #229954);">
                        <h4 class="mb-0 text-white">
                            <i class="fas fa-user-check me-3"></i>Currently Logged In Users
                            <span class="badge bg-light text-dark ms-3">{{ current_users|length }} Active</span>
                        </h4>
                    </div>
                    <div class="card-body p-4" id="current-users-list">
                        {% if current_users %}
                            <div class="row">
                                {% for user in current_users %}
                                <div class="col-lg-6 col-md-12 mb-3">
                                    <div class="user-row">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div class="flex-grow-1">
                                                <div class="user-name">
                                                    <i class="fas fa-user-circle me-2 text-success"></i>
                                                    <a href="/user/{{ user.user_id }}" class="text-decoration-none">
                                                        {{ user.name }}
                                                    </a>
                                                </div>
                                                <div class="user-details">
                                                    <i class="fas fa-clock me-1"></i> {{ user.login_time }}
                                                    <span class="mx-2">•</span>
                                                    <i class="fas fa-video me-1"></i> {{ user.camera_name }}
                                                </div>
                                            </div>
                                            <div class="ms-3">
                                                <span class="badge {% if user.camera_type == 'IN' %}bg-success{% else %}bg-warning{% endif %} px-3 py-2">
                                                    {{ user.camera_type }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-user-slash fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No users currently logged in</h5>
                                <p class="text-muted">Users will appear here when they check in through the cameras</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Active Cameras Control Panel -->
            <div class="col-lg-4 col-md-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-video me-2"></i>Camera Control Panel</h5>
                    </div>
                    <div class="card-body">
                        {% if active_cameras %}
                            {% for camera in active_cameras %}
                            <div class="d-flex justify-content-between align-items-center mb-3 p-3 bg-light rounded">
                                <div class="flex-grow-1">
                                    <div class="d-flex align-items-center mb-1">
                                        <span class="camera-status {% if camera.is_active %}camera-active{% else %}camera-inactive{% endif %}"></span>
                                        <strong class="text-dark">{{ camera.camera_name }}</strong>
                                    </div>
                                    <small class="text-muted">
                                        <i class="fas fa-tag me-1"></i>{{ camera.camera_type }}
                                    </small>
                                </div>
                                <div class="ms-2">
                                    <button class="btn btn-sm btn-outline-primary me-1" onclick="viewCamera({{ camera.camera_id }})" title="View Camera">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" onclick="stopCamera({{ camera.camera_id }})" title="Stop Camera">
                                        <i class="fas fa-stop"></i>
                                    </button>
                                </div>
                            </div>
                            {% endfor %}
                            <div class="text-center mt-3">
                                <a href="/manage-camera" class="btn btn-primary btn-sm">
                                    <i class="fas fa-cog me-1"></i>Manage Cameras
                                </a>
                            </div>
                        {% else %}
                            <div class="text-center py-4">
                                <i class="fas fa-video-slash fa-2x text-muted mb-3"></i>
                                <p class="text-muted mb-3">No active cameras</p>
                                <a href="/manage-camera" class="btn btn-primary btn-sm">
                                    <i class="fas fa-plus me-1"></i>Add Camera
                                </a>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Attendance Logs -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center bg-light">
                        <h5 class="mb-0 text-dark">
                            <i class="fas fa-history me-2"></i>Recent Attendance Logs
                            <small class="text-muted ms-2">(Today's Last 5 Records)</small>
                        </h5>
                        <a href="/attendance" class="btn btn-primary btn-sm">
                            <i class="fas fa-list me-1"></i>View All
                        </a>
                    </div>
                    <div class="card-body p-0">
                        {% if today_last_5 %}
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th class="border-0">Employee</th>
                                            <th class="border-0">Date</th>
                                            <th class="border-0">Check In</th>
                                            <th class="border-0">Check Out</th>
                                            <th class="border-0">Duration</th>
                                            <th class="border-0">Camera</th>
                                            <th class="border-0">Status</th>
                                        </tr>
                                    </thead>
                                    <tbody id="recent-attendance-tbody">
                                        {% for log in today_last_5 %}
                                        <tr>
                                            <td class="py-3">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-user-circle fa-lg text-primary me-2"></i>
                                                    <a href="/user/{{ log.user_id }}" class="text-decoration-none fw-semibold">
                                                        {{ log.name }}
                                                    </a>
                                                </div>
                                            </td>
                                            <td class="py-3">
                                                <small class="text-muted">{{ log.date }}</small>
                                            </td>
                                            <td class="py-3">
                                                <span class="{% if log.login_time %}text-success{% else %}text-muted{% endif %}">
                                                    {{ log.login_time or '-' }}
                                                </span>
                                            </td>
                                            <td class="py-3">
                                                <span class="{% if log.logout_time %}text-danger{% else %}text-muted{% endif %}">
                                                    {{ log.logout_time or 'Active' }}
                                                </span>
                                            </td>
                                            <td class="py-3">
                                                <span class="fw-semibold">{{ log.duration or 'In Progress' }}</span>
                                            </td>
                                            <td class="py-3">
                                                <small class="text-muted">{{ log.camera_name }}</small>
                                            </td>
                                            <td class="py-3">
                                                <span class="badge {% if log.camera_type == 'IN' %}bg-success{% else %}bg-warning{% endif %} px-3">
                                                    {{ log.camera_type }}
                                                </span>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No attendance records for today</h5>
                                <p class="text-muted">Attendance logs will appear here as users check in and out</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function viewCamera(cameraId) {
            window.open(`/camera-stream/${cameraId}`, '_blank', 'width=800,height=600');
        }

        function stopCamera(cameraId) {
            if (confirm('Are you sure you want to stop this camera?')) {
                fetch(`/stop-camera/${cameraId}`, { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        alert(data.message || data.error);
                        location.reload();
                    })
                    .catch(error => {
                        alert('Error stopping camera');
                        console.error(error);
                    });
            }
        }

        // Handle camera feed errors
        function handleCameraError(img) {
            console.log('Camera feed error for camera:', img.dataset.cameraId);
            img.style.display = 'none';
            img.nextElementSibling.style.display = 'flex';

            // Try to reconnect after 3 seconds
            setTimeout(() => {
                const cameraId = img.dataset.cameraId;
                const newSrc = `/camera-stream/${cameraId}?t=${new Date().getTime()}`;
                img.src = newSrc;
            }, 3000);
        }

        // Initialize camera feeds with proper error handling
        function initializeCameraFeeds() {
            const cameraFeeds = document.querySelectorAll('.camera-feed');
            cameraFeeds.forEach(feed => {
                // Add timestamp to prevent caching
                const cameraId = feed.dataset.cameraId;
                feed.src = `/camera-stream/${cameraId}?t=${new Date().getTime()}`;

                // Set up periodic refresh for each feed
                setInterval(() => {
                    if (feed.style.display !== 'none') {
                        const newSrc = `/camera-stream/${cameraId}?t=${new Date().getTime()}`;
                        feed.src = newSrc;
                    }
                }, 100); // Refresh every 100ms for smooth video
            });
        }

        // Initialize feeds when page loads
        document.addEventListener('DOMContentLoaded', initializeCameraFeeds);

        // Real-time dashboard updates
        function updateDashboardData() {
            // Update recent attendance logs
            fetch('/api/attendance/recent')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        updateRecentAttendanceLogs(data.logs, data.today_total);
                    }
                })
                .catch(error => console.error('Error fetching recent attendance:', error));

            // Update currently logged in users
            fetch('/api/attendance/current')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        updateCurrentlyLoggedIn(data.users);
                    }
                })
                .catch(error => console.error('Error fetching current users:', error));
        }

        function updateRecentAttendanceLogs(logs, todayTotal) {
            // Update recent attendance table
            const recentTbody = document.querySelector('#recent-attendance-tbody');
            if (recentTbody) {
                recentTbody.innerHTML = '';
                logs.forEach(log => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td><a href="/user/${log.user_id}" class="text-decoration-none">${log.name}</a></td>
                        <td>${log.date}</td>
                        <td>${log.login_time || '-'}</td>
                        <td>${log.logout_time || '-'}</td>
                        <td>${log.duration || '-'}</td>
                        <td>${log.camera_name}</td>
                        <td>
                            <span class="badge ${log.camera_type === 'IN' ? 'bg-success' : 'bg-warning'}">
                                ${log.camera_type}
                            </span>
                        </td>
                    `;
                    recentTbody.appendChild(row);
                });
            }

            // Update today's attendance count in stats card using the correct selector
            const todayCountElement = document.querySelector('#today-attendance-count');
            if (todayCountElement && todayTotal !== undefined) {
                todayCountElement.textContent = todayTotal;
            }
        }

        function updateCurrentlyLoggedIn(users) {
            const currentUsersContainer = document.querySelector('#current-users-list');
            const currentUsersCount = document.querySelector('.card-stats:nth-child(1) h2');
            const currentUsersTbody = document.querySelector('#current-users-tbody');

            if (currentUsersCount) {
                currentUsersCount.textContent = users.length;
            }

            if (currentUsersContainer) {
                if (users.length === 0) {
                    currentUsersContainer.innerHTML = '<p class="text-muted">No users currently logged in.</p>';
                } else {
                    // Update the table structure
                    currentUsersContainer.innerHTML = `
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Login Time</th>
                                        <th>Camera</th>
                                        <th>Type</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${users.map(user => `
                                        <tr>
                                            <td>
                                                <a href="/user/${user.user_id}" class="text-decoration-none">
                                                    ${user.name}
                                                </a>
                                            </td>
                                            <td>${user.login_time}</td>
                                            <td>${user.camera_name}</td>
                                            <td>
                                                <span class="badge bg-${user.camera_type === 'IN' ? 'success' : 'warning'}">
                                                    ${user.camera_type}
                                                </span>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    `;
                }
            }
        }

        // Update dashboard data every 3 seconds for real-time updates
        setInterval(updateDashboardData, 3000);

        // Initial update when page loads
        document.addEventListener('DOMContentLoaded', () => {
            updateDashboardData();
        });

        // Full page refresh every 5 minutes as backup
        setInterval(() => {
            location.reload();
        }, 300000);
    </script>
</body>
</html>
